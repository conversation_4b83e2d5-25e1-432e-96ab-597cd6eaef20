const express = require('express');
const User = require('../models/User');
const ActivationKey = require('../models/ActivationKey');
const Activity = require('../models/Activity');
const Diagnosis = require('../models/Diagnosis');
const SyncLog = require('../models/SyncLog');
const { 
  authenticateToken, 
  requireAdmin,
  requireSupervisor,
  logRequest
} = require('../middleware/auth');
const { 
  validateActivationKeyCreation,
  validatePagination,
  validateDateRange,
  validateObjectId
} = require('../middleware/validation');

const router = express.Router();

// Apply middleware to all admin routes
router.use(logRequest);

// Simple admin authentication middleware - just check for admin in the request
const simpleAdminAuth = (req, res, next) => {
  // For demo purposes, allow admin access without complex authentication
  // In production, you'd have proper admin authentication
  req.user = {
    _id: 'admin-user-id',
    role: 'admin',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User'
  };
  next();
};

// Apply simple admin auth to routes that need it
const protectedRoutes = ['/users', '/activation-keys', '/analytics', '/system'];
router.use(protectedRoutes, simpleAdminAuth);

/**
 * GET /api/v1/admin/dashboard/stats
 * Get dashboard statistics for admin panel
 */
router.get('/dashboard/stats', requireSupervisor, validateDateRange, async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date()
    } = req.query;

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Get user statistics
    const totalUsers = await User.countDocuments({ isActive: true });
    const newUsers = await User.countDocuments({
      createdAt: { $gte: start, $lte: end }
    });
    const activeUsers = await Activity.distinct('userId', {
      timestamp: { $gte: start, $lte: end }
    });

    // Get activation key statistics
    const totalKeys = await ActivationKey.countDocuments();
    const activeKeys = await ActivationKey.countDocuments({ status: 'active' });
    const usedKeys = await ActivationKey.countDocuments({ status: 'used' });
    const expiredKeys = await ActivationKey.countDocuments({ status: 'expired' });

    // Get activity statistics
    const totalActivities = await Activity.countDocuments({
      timestamp: { $gte: start, $lte: end }
    });
    const errorActivities = await Activity.countDocuments({
      activityType: 'error',
      timestamp: { $gte: start, $lte: end }
    });

    // Get diagnosis statistics
    const totalDiagnoses = await Diagnosis.countDocuments({
      createdAt: { $gte: start, $lte: end }
    });
    const completedDiagnoses = await Diagnosis.countDocuments({
      status: 'completed',
      createdAt: { $gte: start, $lte: end }
    });

    // Get sync statistics
    const totalSyncs = await SyncLog.countDocuments({
      startedAt: { $gte: start, $lte: end }
    });
    const failedSyncs = await SyncLog.countDocuments({
      status: 'failed',
      startedAt: { $gte: start, $lte: end }
    });

    // Get user role breakdown
    const userRoles = await User.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    // Get top error codes
    const topErrors = await Activity.aggregate([
      {
        $match: {
          activityType: 'error',
          timestamp: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: '$error.code',
          count: { $sum: 1 },
          severity: { $first: '$error.severity' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        period: { startDate: start, endDate: end },
        users: {
          total: totalUsers,
          new: newUsers,
          active: activeUsers.length,
          roleBreakdown: userRoles.reduce((acc, role) => {
            acc[role._id] = role.count;
            return acc;
          }, {})
        },
        activationKeys: {
          total: totalKeys,
          active: activeKeys,
          used: usedKeys,
          expired: expiredKeys
        },
        activities: {
          total: totalActivities,
          errors: errorActivities,
          errorRate: totalActivities > 0 ? (errorActivities / totalActivities * 100).toFixed(2) : 0
        },
        diagnoses: {
          total: totalDiagnoses,
          completed: completedDiagnoses,
          completionRate: totalDiagnoses > 0 ? (completedDiagnoses / totalDiagnoses * 100).toFixed(2) : 0
        },
        sync: {
          total: totalSyncs,
          failed: failedSyncs,
          successRate: totalSyncs > 0 ? ((totalSyncs - failedSyncs) / totalSyncs * 100).toFixed(2) : 0
        },
        topErrors
      }
    });

  } catch (error) {
    console.error('Get admin dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve dashboard statistics',
      code: 'GET_ADMIN_DASHBOARD_STATS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/users
 * Get all users with pagination and filtering
 */
router.get('/users', requireSupervisor, validatePagination, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } },
        { facility: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) query.role = role;
    if (status === 'active') query.isActive = true;
    if (status === 'inactive') query.isActive = false;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const users = await User.find(query)
      .select('-password -activationKey')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get admin users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve users',
      code: 'GET_ADMIN_USERS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/users/:userId
 * Get detailed user information
 */
router.get('/users/:userId', requireSupervisor, validateObjectId('userId'), async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId)
      .select('-password')
      .lean();

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Get user's recent activities
    const recentActivities = await Activity.find({ userId })
      .sort({ timestamp: -1 })
      .limit(10)
      .select('activityType timestamp screenName error')
      .lean();

    // Get user's diagnosis count
    const diagnosisCount = await Diagnosis.countDocuments({ userId });

    // Get user's sync statistics
    const syncStats = await SyncLog.aggregate([
      { $match: { userId: user._id } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        user,
        statistics: {
          diagnosisCount,
          recentActivities,
          syncStats: syncStats.reduce((acc, stat) => {
            acc[stat._id] = stat.count;
            return acc;
          }, {})
        }
      }
    });

  } catch (error) {
    console.error('Get admin user details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve user details',
      code: 'GET_ADMIN_USER_DETAILS_ERROR'
    });
  }
});

/**
 * PUT /api/v1/admin/users/:userId/status
 * Update user status (activate/deactivate)
 */
router.put('/users/:userId/status', requireAdmin, validateObjectId('userId'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive, reason } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'isActive must be a boolean',
        code: 'INVALID_STATUS'
      });
    }

    const user = await User.findByIdAndUpdate(
      userId,
      {
        isActive,
        $push: {
          'metadata.statusHistory': {
            status: isActive ? 'activated' : 'deactivated',
            reason: reason || 'Admin action',
            timestamp: new Date(),
            adminId: req.user._id
          }
        }
      },
      { new: true }
    ).select('-password -activationKey');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      data: { user }
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user status',
      code: 'UPDATE_USER_STATUS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/activation-keys
 * Get activation keys with pagination and filtering
 */
router.get('/activation-keys', requireSupervisor, validatePagination, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { 'assignedTo.email': { $regex: search, $options: 'i' } },
        { 'assignedTo.fullName': { $regex: search, $options: 'i' } },
        { 'assignedTo.facility': { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const keys = await ActivationKey.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('assignedTo.userId', 'firstName lastName email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const total = await ActivationKey.countDocuments(query);

    res.json({
      success: true,
      data: {
        activationKeys: keys,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get admin activation keys error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve activation keys',
      code: 'GET_ADMIN_ACTIVATION_KEYS_ERROR'
    });
  }
});

/**
 * POST /api/v1/admin/activation-keys
 * Create new activation key
 */
router.post('/activation-keys', requireAdmin, validateActivationKeyCreation, async (req, res) => {
  try {
    const {
      assignedTo,
      validUntil,
      maxUses = 1,
      notes,
      locationRestrictions
    } = req.body;

    // Generate unique activation key
    let key;
    let isUnique = false;
    let attempts = 0;
    
    while (!isUnique && attempts < 10) {
      key = ActivationKey.generateKey();
      const existing = await ActivationKey.findByKey(key);
      if (!existing) {
        isUnique = true;
      }
      attempts++;
    }

    if (!isUnique) {
      return res.status(500).json({
        success: false,
        error: 'Failed to generate unique activation key',
        code: 'KEY_GENERATION_ERROR'
      });
    }

    const activationKey = new ActivationKey({
      key,
      assignedTo,
      validUntil: new Date(validUntil),
      maxUses,
      notes,
      locationRestrictions,
      createdBy: req.user._id,
      status: 'active'
    });

    await activationKey.save();

    // Add creation to usage history
    await activationKey.addUsageHistory(
      'created',
      req.user._id,
      null,
      req.ip,
      null,
      { notes }
    );

    res.status(201).json({
      success: true,
      message: 'Activation key created successfully',
      data: {
        activationKey: {
          id: activationKey._id,
          key: activationKey.key,
          assignedTo: activationKey.assignedTo,
          validUntil: activationKey.validUntil,
          status: activationKey.status,
          createdAt: activationKey.createdAt
        }
      }
    });

  } catch (error) {
    console.error('Create activation key error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create activation key',
      code: 'CREATE_ACTIVATION_KEY_ERROR'
    });
  }
});

/**
 * POST /api/v1/admin/activation-keys/:keyId/revoke
 * Revoke an activation key
 */
router.post('/activation-keys/:keyId/revoke', requireAdmin, validateObjectId('keyId'), async (req, res) => {
  try {
    const { keyId } = req.params;
    const { reason } = req.body;

    const activationKey = await ActivationKey.findById(keyId);
    if (!activationKey) {
      return res.status(404).json({
        success: false,
        error: 'Activation key not found',
        code: 'ACTIVATION_KEY_NOT_FOUND'
      });
    }

    if (activationKey.status === 'revoked') {
      return res.status(400).json({
        success: false,
        error: 'Activation key is already revoked',
        code: 'ALREADY_REVOKED'
      });
    }

    await activationKey.revoke(req.user._id, reason || 'Admin revocation');

    res.json({
      success: true,
      message: 'Activation key revoked successfully',
      data: {
        keyId: activationKey._id,
        revokedAt: activationKey.revokedAt,
        reason: activationKey.revocationReason
      }
    });

  } catch (error) {
    console.error('Revoke activation key error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to revoke activation key',
      code: 'REVOKE_ACTIVATION_KEY_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/analytics/errors
 * Get error analytics
 */
router.get('/analytics/errors', requireSupervisor, validateDateRange, async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      endDate = new Date()
    } = req.query;

    const errorStats = await Activity.getErrorStats(
      new Date(startDate),
      new Date(endDate)
    );

    // Get error trends by day
    const errorTrends = await Activity.aggregate([
      {
        $match: {
          activityType: 'error',
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          count: { $sum: 1 },
          criticalCount: {
            $sum: {
              $cond: [{ $eq: ['$error.severity', 'critical'] }, 1, 0]
            }
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    res.json({
      success: true,
      data: {
        period: { startDate: new Date(startDate), endDate: new Date(endDate) },
        errorStats,
        errorTrends
      }
    });

  } catch (error) {
    console.error('Get error analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve error analytics',
      code: 'GET_ERROR_ANALYTICS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/analytics/usage
 * Get usage analytics
 */
router.get('/analytics/usage', requireSupervisor, validateDateRange, async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date()
    } = req.query;

    // Get daily active users
    const dailyActiveUsers = await Activity.aggregate([
      {
        $match: {
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          users: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          userCount: { $size: '$users' }
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get feature usage
    const featureUsage = await Activity.aggregate([
      {
        $match: {
          timestamp: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$activityType',
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          activityType: '$_id',
          count: 1,
          uniqueUsers: { $size: '$uniqueUsers' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        period: { startDate: new Date(startDate), endDate: new Date(endDate) },
        dailyActiveUsers,
        featureUsage
      }
    });

  } catch (error) {
    console.error('Get usage analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve usage analytics',
      code: 'GET_USAGE_ANALYTICS_ERROR'
    });
  }
});

/**
 * GET /api/v1/admin/system/health
 * Get system health status
 */
router.get('/system/health', requireSupervisor, async (req, res) => {
  try {
    // Get database connection status
    const mongoose = require('mongoose');
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // Get recent error rate
    const recentErrors = await Activity.countDocuments({
      activityType: 'error',
      timestamp: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const recentActivities = await Activity.countDocuments({
      timestamp: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const errorRate = recentActivities > 0 ? (recentErrors / recentActivities * 100).toFixed(2) : 0;

    // Get sync health
    const recentSyncs = await SyncLog.countDocuments({
      startedAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const failedSyncs = await SyncLog.countDocuments({
      status: 'failed',
      startedAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
    });

    const syncSuccessRate = recentSyncs > 0 ? ((recentSyncs - failedSyncs) / recentSyncs * 100).toFixed(2) : 100;

    // Get active users in last 24 hours
    const activeUsers = await Activity.distinct('userId', {
      timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    res.json({
      success: true,
      data: {
        timestamp: new Date(),
        database: {
          status: dbStatus,
          healthy: dbStatus === 'connected'
        },
        errors: {
          recentCount: recentErrors,
          rate: parseFloat(errorRate),
          healthy: parseFloat(errorRate) < 5 // Less than 5% error rate is healthy
        },
        sync: {
          recentCount: recentSyncs,
          successRate: parseFloat(syncSuccessRate),
          healthy: parseFloat(syncSuccessRate) > 95 // More than 95% success rate is healthy
        },
        users: {
          active24h: activeUsers.length
        },
        overall: {
          healthy: dbStatus === 'connected' && parseFloat(errorRate) < 5 && parseFloat(syncSuccessRate) > 95
        }
      }
    });

  } catch (error) {
    console.error('Get system health error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve system health',
      code: 'GET_SYSTEM_HEALTH_ERROR'
    });
  }
});

module.exports = router;
